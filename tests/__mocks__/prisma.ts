/**
 * Prisma Client Mock
 * Mock implementation of Prisma Client for testing
 */

// Create mock functions for all Prisma operations
const createMockModel = () => ({
  findUnique: jest.fn(),
  findMany: jest.fn(),
  create: jest.fn(),
  createMany: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  count: jest.fn(),
  upsert: jest.fn(),
  deleteMany: jest.fn(),
  updateMany: jest.fn(),
  aggregate: jest.fn(),
  groupBy: jest.fn(),
  findFirst: jest.fn(),
  findFirstOrThrow: jest.fn(),
  findUniqueOrThrow: jest.fn(),
});

// Mock Prisma Client
export const mockPrisma = {
  user: createMockModel(),
  product: createMockModel(),
  category: createMockModel(),
  order: createMockModel(),
  orderItem: createMockModel(),
  cart: createMockModel(),
  cartItem: createMockModel(),
  address: createMockModel(),
  review: createMockModel(),
  wishlist: createMockModel(),
  wishlistItem: createMockModel(),
  // Add missing models for audit logs and notifications
  auditLog: createMockModel(),
  notification: createMockModel(),
  adminUser: createMockModel(),
  brand: createMockModel(),
  post: createMockModel(),
  page: createMockModel(),
  setting: createMockModel(),
  inventory: createMockModel(),
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  $transaction: jest.fn(),
  $executeRaw: jest.fn(),
  $queryRaw: jest.fn(),
};

// Mock bcrypt
export const mockBcrypt = {
  hash: jest.fn(),
  compare: jest.fn(),
  genSalt: jest.fn(),
  hashSync: jest.fn(),
  compareSync: jest.fn(),
  genSaltSync: jest.fn(),
};

// Export types for TypeScript
export type MockPrisma = typeof mockPrisma;
export type MockBcrypt = typeof mockBcrypt;
